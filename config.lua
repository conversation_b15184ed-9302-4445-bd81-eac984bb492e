Config = {
  locale                = 'de',
  debug                 = false,
  dayNightSwitch        = true,
  enableStatusHud       = true,
  chat                  = {
    enabled = true,
    defaultMapping = 'T'
  },
  serverName            = {
    primary = 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ',        -- the blue text
    secondary = '' -- optional
  },
  disabledHudComponents = {
    1, 2, 3, 4, 5, 6, 7, 8, 9
  },
  deathTimeoutTimer     = 15 * 60, -- in seconds
  enableKillfeed        = true,
  permissions           = {
    killfeed = {
      'admin'
    },
    teamchat = {
      'admin'
    },
    announce = {
      'admin'
    },
    deathtimeout = {
      'admin'
    }
  },
  commands              = {
    id = true,
    ids = true,
    names = {
      id = 'id',
      ids = 'ids',
      chat = 'chat',
      hudsettings = 'hudsettings',
      teamchat = 'tc',
      announce = 'announce',
      killfeed = 'showkillfeed',
      resetdeathtimeout = 'resetdeathtimeout'
    }
  },
  voice                 = {
    type = GetResourceState('saltychat') ~= 'missing' and 'saltychat' or
        GetResourceState('pma-voice') ~= 'missing' and 'pma-voice' or 'none',
    circle = {
      enabled = true,
      smooth = true,
      color = { r = 0, g = 128, b = 255, a = 255 }
    },
    notification = true
  },
  sounds                = {
    notification = {
      warn = { 'ERROR', 'HUD_FRONTEND_DEFAULT_SOUNDSET' },
      info = { 'ATM_WINDOW', 'HUD_FRONTEND_DEFAULT_SOUNDSET' },
      error = { 'ERROR', 'HUD_FRONTEND_DEFAULT_SOUNDSET' },
      success = { 'ATM_WINDOW', 'HUD_FRONTEND_DEFAULT_SOUNDSET' },
      teamchat = { 'ATM_WINDOW', 'HUD_FRONTEND_DEFAULT_SOUNDSET' }
    },
    announce = { 'CHALLENGE_UNLOCKED', 'HUD_AWARDS' },
    helpNotify = {
      fadeIn = { 'INFO', 'HUD_FRONTEND_DEFAULT_SOUNDSET' },
      fadeOut = { 'CANCEL', 'HUD_FRONTEND_DEFAULT_SOUNDSET' }
    }
  },
  events                = {
    status = {
      add = 'esx_status:add',
      remove = 'esx_status:remove',
      set = 'esx_status:set',
      onTick = 'esx_status:onTick',
      load = 'esx_status:load',
      getStatus = 'esx_status:getStatus'
    },
    setAccountMoney = 'esx:setAccountMoney',
    setJob = 'esx:setJob',
    -- TriggerEvent('esx_ambulancejob:revive', true) = no deathtimeout
    -- TriggerEvent('esx_ambulancejob:revive', false/nil) = deathtimeout
    revive = 'esx_ambulancejob:revive'
  }
};

if not IsDuplicityVersion() then
  ---@param name string
  ---@param value number
  function CalculateStatus(name, value)
    return math.floor(value / 10000);
  end
  
  ---@return boolean
  function CancelDeathtimeout()
    -- Example:
    -- if exports.ffa:isInFFA() or exports.gangwar:isInGangwar() then
    --   return true;
    -- end

    return false;
  end

  RegisterCommand('testnotify', function()
    TriggerEvent('a_hud::AddNotification', {
      type = 'info',
      title = 'Information',
      text = 'This is a test info notification',
      time = 5000
    });

    Citizen.Wait(1000);

    TriggerEvent('a_hud::AddNotification', {
      type = 'error',
      title = 'Error',
      text = 'This is a test error notification',
      time = 5000
    });

    Citizen.Wait(1000);

    TriggerEvent('a_hud::AddNotification', {
      type = 'success',
      title = 'Success',
      text = 'This is a test success notification',
      time = 5000
    });

    Citizen.Wait(1000);

    TriggerEvent('a_hud::AddNotification', {
      type = 'warn',
      title = 'Warning',
      text = 'This is a test warning notification',
      time = 5000
    });

    Citizen.Wait(1000);

    TriggerEvent('a_hud::AddNotification', {
      type = 'teamchat',
      title = 'Team Chat',
      text = 'This is a test team chat notification',
      time = 5000
    });

    Citizen.Wait(2500);

    TriggerEvent('a_hud::AddKillfeed', {
      killer = 'zHemo',
      victim = 'zYasin',
      time = 5000
    })

    Citizen.Wait(2500);

    TriggerEvent('a_hud::AddAnnouncement', {
      title = 'Announcement',
      text = 'This is a test announcement',
      time = 5000
    });

    Citizen.Wait(2500);

    TriggerEvent('a_hud::HelpNotification', 'E', 'This is a test help notification')

    Citizen.Wait(4000);

    TriggerEvent('a_hud::StartProgressBar', 5000, 'Ziehe Test-Weste...');

  end, false);

  RegisterCommand('day', function()
    NetworkOverrideClockTime(12, 0, 0);
  end, false);

  RegisterCommand('night', function()
    NetworkOverrideClockTime(3, 0, 0);
  end, false);
end
