# a_hud

## Events

### a_hud::ToggleHud

```lua
<PERSON>gger<PERSON>vent('a_hud::ToggleHud', display)
```

- display: `boolean`

### a_hud::ToggleHud

- display: `boolean`

```lua
TriggerEvent('a_hud::ToggleHud', display)
```

### a_hud::StartProgressBar

- time: `number`
- label: `string`

```lua
TriggerEvent('a_hud::StartProgressBar', time, label)
```

### a_hud::StartProgressBar

```lua
TriggerEvent('a_hud::StopProgressBar')
```

### a_hud::AddNotification

- data: `table`
  - type?: `string` ('notify', 'info', 'error', 'success')
  - title: `string`
  - text: `string`
  - time?: `number`

```lua
TriggerEvent('a_hud::AddNotification', {
  type = 'info',
  title = 'Notification',
  text = 'This is a notification',
  time = 5000
})
-- or
-- type will default to 'info', title to 'Information', and time to 5000
TriggerEvent('a_hud::AddNotification', text)
-- or
TriggerEvent('a_hud::AddNotification', type, title, text, time)
```

### a_hud::AddAnnouncement

- data: `table`
  - title: `string`
  - text: `string`
  - time?: `number`

```lua
TriggerEvent('a_hud::AddAnnouncement', {
  title = 'Announcement',
  text = 'This is an announcement',
  time = 11000
})
-- or
-- title will default to announce.title in locales, and time to 11000
TriggerEvent('a_hud::AddAnnouncement', text)
-- or
TriggerEvent('a_hud::AddAnnouncement', title, text, time)
```

### a_hud::AddKillfeed

- data: `table`
  - killer: `string`
  - victim: `string`
  - time?: `number`

```lua
TriggerEvent('a_hud::AddKillfeed', {
  killer = 'Killer',
  victim = 'Victim',
  time = 5000
})
```

### a_hud::HelpNotification

- data: `table`
  - key?: `string`
  - text?: `string`

```lua
TriggerEvent('a_hud::HelpNotification', {
  key = 'E',
  text = 'This is a help notification'
})
-- or
TriggerEvent('a_hud::HelpNotification', 'E', 'This is a help notification')
```

### a_hud::ToggleHelpNotification

- data: `table`
  - display: `boolean`
  - key?: `string`
  - text?: `string`

```lua
TriggerEvent('a_hud::ToggleHelpNotification', {
  display = true,
  key = 'E',
  text = 'This is a help notification'
})
-- or
TriggerEvent('a_hud::HelpNotification', true, 'E', 'This is a help notification')
```

### a_hud::SetEntryCount

Use this event to set the entry count for admins.

- count: `number`

```lua
TriggerEvent('a_hud::SetEntryCount', count)
```

## Exports

### ToggleHud

- display: `boolean`

```lua
exports.a_hud:ToggleHud(display)
```

### StartProgressBar

- time: `number`
- label: `string`

```lua
exports.a_hud:StartProgressBar(time, label)
```

### StopProgressBar

```lua
exports.a_hud:StopProgressBar()
```

### HelpNotification

- data: `table`
  - display: `boolean`
  - key?: `string`
  - text: `string`

```lua
exports.a_hud:HelpNotification({
  display = true,
  key = 'E',
  text = 'This is a help notification'
})
```

### ToggleHelpNotification

- data: `table`
  - key?: `string`
  - text: `string`

```lua
exports.a_hud:ToggleHelpNotification({
  key = 'E',
  text = 'This is a help notification'
})
```
